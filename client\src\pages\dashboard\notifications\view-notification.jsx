import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useNotification } from "@/context/notification-context";
import { PageHeader } from "@/components/dashboard/page-header";
import {
  <PERSON>,
  CardHeader,
  <PERSON><PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import { StatusBadge } from "@/components/data-card/data-card-component/status-badge";
import {
  Bell,
  AlertTriangle,
  Calendar,
  Clock,
  ArrowLeft,
  Edit,
  Send,
  Trash2,
} from "lucide-react";

const ViewNotification = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchNotificationById, removeNotification, sendNotificationNow } =
    useNotification();
  const [notification, setNotification] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openSend, setOpenSend] = useState(false);

  useEffect(() => {
    const fetchNotification = async () => {
      try {
        setIsLoading(true);
        const data = await fetchNotificationById(id);
        setNotification(data);
      } catch (error) {
        console.error("Failed to fetch notification:", error);
        toast.error("Failed to load notification details");
        navigate("/dashboard/notifications");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchNotification();
    }
  }, [id, fetchNotificationById, navigate]);

  const handleDelete = async () => {
    try {
      if (notification.status === "sent") {
        toast.error("Cannot delete a notification that has already been sent");
        setOpenDelete(false);
        return;
      }
      await removeNotification(notification._id);
      toast.success(`${notification.title} deleted successfully.`);
      navigate("/dashboard/notifications");
    } catch (error) {
      console.error("Failed to delete notification:", error);
      toast.error("Failed to delete notification. Please try again.");
    }
  };

  const handleSend = async () => {
    try {
      if (notification.status === "sent") {
        toast.error("Notification has already been sent");
        setOpenSend(false);
        return;
      }
      await sendNotificationNow(notification._id);
      toast.success(`${notification.title} sent successfully.`);
      // Refresh notification data
      const updatedNotification = await fetchNotificationById(id);
      setNotification(updatedNotification);
      setOpenSend(false);
    } catch (error) {
      console.error("Failed to send notification:", error);
      toast.error("Failed to send notification. Please try again.");
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case "Emergency":
        return <AlertTriangle className="h-5 w-5 text-destructive" />;
      case "Event":
        return <Calendar className="h-5 w-5 text-success" />;
      case "Academic":
        return <Clock className="h-5 w-5 text-warning" />;
      default:
        return <Bell className="h-5 w-5 text-primary" />;
    }
  };

  if (isLoading) {
    return <NotificationDetailSkeleton />;
  }

  if (!notification) {
    return (
      <div className="flex flex-col">
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          <PageHeader
            title="Notification Not Found"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Notifications", href: "/dashboard/notifications" },
              { label: "Not Found" },
            ]}
            actions={[
              {
                label: "Back to Notifications",
                icon: ArrowLeft,
                href: "/dashboard/notifications",
              },
            ]}
          />
          <Card>
            <CardContent className="py-10">
              <div className="text-center">
                <Bell className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">
                  Notification not found
                </h3>
                <p className="mt-2 text-muted-foreground">
                  The notification you're looking for doesn't exist or has been
                  removed.
                </p>
                <Button
                  className="mt-4"
                  onClick={() => navigate("/dashboard/notifications")}
                >
                  Back to Notifications
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          title="Notification Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Notifications", href: "/dashboard/notifications" },
            { label: notification.title },
          ]}
          actions={[
            ...(notification.status !== "sent"
              ? [
                  {
                    label: "Edit",
                    icon: Edit,
                    href: `/dashboard/notifications/${notification._id}/edit`,
                  },
                ]
              : []),
            {
              label: "Back",
              icon: ArrowLeft,
              href: "/dashboard/notifications",
            },
          ]}
        />

        <Card>
          <CardHeader className="flex flex-row items-center gap-4 border-b pb-4">
            <div className="flex-shrink-0">
              {getNotificationIcon(notification.type)}
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">{notification.title}</h2>
                <div className="flex items-center gap-2">
                  <StatusBadge
                    row={{ original: notification }}
                    statusField="priority"
                    variant={{
                      Low: "success",
                      High: "destructive",
                      Medium: "warning",
                    }}
                  />
                  <StatusBadge
                    row={{ original: notification }}
                    statusField="status"
                    variant={{
                      draft: "secondary",
                      scheduled: "warning",
                      sent: "success",
                      failed: "destructive",
                    }}
                  />
                </div>
              </div>
              <div className="flex items-center text-sm text-muted-foreground mt-1">
                <span className="font-medium capitalize">
                  {notification.recipients}
                </span>
                <span className="mx-1">•</span>
                <span>{notification.type}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="py-6 space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Message</h3>
              <p className="text-foreground/90 whitespace-pre-wrap">
                {notification.message}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-1">
                  Created At
                </h3>
                <p>{formatDate(notification.createdAt)}</p>
              </div>
              {notification.scheduledFor && (
                <div>
                  <h3 className="text-sm font-semibold text-muted-foreground mb-1">
                    Scheduled For
                  </h3>
                  <p>{formatDate(notification.scheduledFor)}</p>
                </div>
              )}
              {notification.sentAt && (
                <div>
                  <h3 className="text-sm font-semibold text-muted-foreground mb-1">
                    Sent At
                  </h3>
                  <p>{formatDate(notification.sentAt)}</p>
                </div>
              )}
              <div>
                <h3 className="text-sm font-semibold text-muted-foreground mb-1">
                  Priority
                </h3>
                <p>{notification.priority}</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4 flex justify-between">
            <Button
              variant="outline"
              onClick={() => navigate("/dashboard/notifications")}
            >
              Back to Notifications
            </Button>
            <div className="flex gap-2">
              {notification.status !== "sent" && (
                <>
                  <Button
                    variant="destructive"
                    onClick={() => setOpenDelete(true)}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </Button>
                  {notification.status === "scheduled" && (
                    <Button onClick={() => setOpenSend(true)}>
                      <Send className="mr-2 h-4 w-4" />
                      Send Now
                    </Button>
                  )}
                  <Button
                    onClick={() =>
                      navigate(
                        `/dashboard/notifications/${notification._id}/edit`
                      )
                    }
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </>
              )}
            </div>
          </CardFooter>
        </Card>
      </main>

      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {notification.title}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this notification? This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={openSend} onOpenChange={setOpenSend}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Send {notification.title} now?</AlertDialogTitle>
            <AlertDialogDescription>
              This will send the notification immediately instead of waiting for
              the scheduled time. Are you sure you want to proceed?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSend}>Send Now</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

// Skeleton Loading Component
const NotificationDetailSkeleton = () => {
  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-10 w-28" />
        </div>

        <Card>
          <CardHeader className="border-b pb-4">
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-6 w-64" />
                <Skeleton className="h-4 w-40" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-6 w-16 rounded-full" />
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="py-6 space-y-6">
            <div className="space-y-2">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-40" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-40" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-40" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-40" />
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t pt-4 flex justify-between">
            <Skeleton className="h-10 w-40" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </CardFooter>
        </Card>
      </main>
    </div>
  );
};

export default ViewNotification;
