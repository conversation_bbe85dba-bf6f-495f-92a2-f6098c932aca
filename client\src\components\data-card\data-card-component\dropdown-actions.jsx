import React from "react";
import {
  DropdownActions,
  createViewAction,
  createEditAction,
  createDeleteAction,
  createSendAction,
  conditions,
} from "./DropdownActions";
import { Archive, Download, Share, Copy, Star } from "lucide-react";

// Example 1: Notification Management (Your Original Use Case)
const NotificationDropdown = ({
  notification,
  onView,
  onEdit,
  onDelete,
  onSend,
}) => {
  const actions = [
    createViewAction(() => onView(notification._id)),
    createSendAction((e, row) => onSend(row.original._id), {
      condition: conditions.statusEquals("scheduled"),
      label: "Send Now",
    }),
    createEditAction(() => onEdit(notification._id), {
      condition: conditions.statusIn(["scheduled", "draft"]),
    }),
    createDeleteAction((e, row) => onDelete(row.original._id), {
      condition: conditions.statusNotEquals("sent"),
    }),
  ];

  return <DropdownActions row={{ original: notification }} actions={actions} />;
};

// Example 2: User Management
const UserDropdown = ({
  user,
  onView,
  onEdit,
  onDeactivate,
  onResetPassword,
}) => {
  const actions = [
    createViewAction(() => onView(user.id)),
    createEditAction(() => onEdit(user.id), {
      condition: (row) => row.original.role !== "admin",
    }),
    {
      key: "reset-password",
      label: "Reset Password",
      icon: Copy,
      onClick: () => onResetPassword(user.id),
      condition: conditions.propertyEquals("status", "active"),
    },
    {
      key: "deactivate",
      label: "Deactivate",
      icon: Archive,
      onClick: () => onDeactivate(user.id),
      variant: "destructive",
      condition: conditions.and(
        conditions.propertyEquals("status", "active"),
        conditions.propertyNotEquals("role", "admin")
      ),
    },
  ];

  return (
    <DropdownActions
      row={{ original: user }}
      actions={actions}
      menuLabel="User Actions"
    />
  );
};

// Example 3: File Management
const FileDropdown = ({ file, onDownload, onShare, onRename, onDelete }) => {
  const actions = [
    {
      key: "download",
      label: "Download",
      icon: Download,
      onClick: () => onDownload(file.id),
    },
    {
      key: "share",
      label: "Share",
      icon: Share,
      onClick: () => onShare(file.id),
      condition: conditions.propertyEquals("visibility", "private"),
    },
    {
      key: "rename",
      label: "Rename",
      icon: Edit,
      onClick: () => onRename(file.id),
      condition: (row) => row.original.permissions?.includes("write"),
    },
    createDeleteAction(() => onDelete(file.id), {
      condition: (row) => row.original.permissions?.includes("delete"),
      disabled: (row) => row.original.isSystemFile,
    }),
  ];

  return (
    <DropdownActions
      row={{ original: file }}
      actions={actions}
      menuLabel="File Actions"
    />
  );
};

// Example 4: Order Management
const OrderDropdown = ({
  order,
  onView,
  onCancel,
  onRefund,
  onMarkComplete,
}) => {
  const actions = [
    createViewAction(() => onView(order.id)),
    {
      key: "mark-complete",
      label: "Mark Complete",
      icon: Star,
      onClick: () => onMarkComplete(order.id),
      condition: conditions.statusEquals("processing"),
    },
    {
      key: "cancel",
      label: "Cancel Order",
      icon: Archive,
      onClick: () => onCancel(order.id),
      variant: "destructive",
      condition: conditions.statusIn(["pending", "processing"]),
    },
    {
      key: "refund",
      label: "Process Refund",
      icon: Copy,
      onClick: () => onRefund(order.id),
      variant: "destructive",
      condition: conditions.and(
        conditions.statusEquals("completed"),
        (row) =>
          new Date(row.original.completedAt) >
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within 30 days
      ),
    },
  ];

  return (
    <DropdownActions
      row={{ original: order }}
      actions={actions}
      menuLabel="Order Actions"
    />
  );
};

// Example 5: Simple actions array (no helper functions)
const SimpleDropdown = ({ item }) => {
  const actions = [
    {
      key: "view",
      label: "View Details",
      icon: Eye,
      onClick: (e, row) => console.log("View:", row.original),
      show: true,
    },
    {
      key: "edit",
      label: "Edit Item",
      icon: Edit,
      onClick: (e, row) => console.log("Edit:", row.original),
      condition: (row) => row.original.canEdit,
    },
    {
      key: "delete",
      label: "Delete Item",
      icon: Trash2,
      onClick: (e, row) => console.log("Delete:", row.original),
      variant: "destructive",
      disabled: (row) => row.original.isProtected,
      condition: (row) => row.original.canDelete,
    },
  ];

  return <DropdownActions row={{ original: item }} actions={actions} />;
};

// Example 6: Custom styling and behavior
const CustomStyledDropdown = ({ item }) => {
  const actions = [
    {
      key: "action1",
      label: "Custom Action",
      icon: Star,
      onClick: (e, row) => console.log("Custom action:", row.original),
      className: "text-blue-600 hover:text-blue-800",
    },
  ];

  return (
    <DropdownActions
      row={{ original: item }}
      actions={actions}
      menuLabel="Custom Menu"
      triggerVariant="outline"
      align="start"
      className="custom-dropdown-wrapper"
      showMenuLabel={false}
      showSeparator={false}
      onActionClick={(e, action, row) => {
        console.log("Global action handler:", action.key, row.original);
      }}
    />
  );
};

export {
  NotificationDropdown,
  UserDropdown,
  FileDropdown,
  OrderDropdown,
  SimpleDropdown,
  CustomStyledDropdown,
};
