import { Card, CardContent } from "../ui/card";

export const DataCardSkeleton = ({ className = "" }) => {
  return (
    <Card className={`border-l-4 border-l-muted ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <div className="h-4 w-4 rounded-full mt-1 bg-muted animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="flex items-center justify-between">
                <div className="h-4 w-48 bg-muted animate-pulse rounded" />
                <div className="flex items-center space-x-2">
                  <div className="h-5 w-12 bg-muted animate-pulse rounded-full" />
                  <div className="h-2 w-2 bg-muted animate-pulse rounded-full" />
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                <div className="h-3 w-1 bg-muted animate-pulse rounded" />
                <div className="h-3 w-16 bg-muted animate-pulse rounded" />
              </div>
              <div className="h-4 w-full bg-muted animate-pulse rounded" />
              <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
              <div className="flex items-center justify-between pt-1">
                <div className="h-3 w-16 bg-muted animate-pulse rounded" />
                <div className="h-3 w-20 bg-muted animate-pulse rounded" />
              </div>
            </div>
          </div>
          <div className="h-8 w-8 bg-muted animate-pulse rounded-md" />
        </div>
      </CardContent>
    </Card>
  );
};
