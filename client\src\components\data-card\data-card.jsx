import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { MoreVertical } from "lucide-react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { DataCardSkeleton } from "./data-card-skeleton";

export const DataCard = ({
  item,
  renderIcon,
  renderBadges,
  renderContent,
  renderFooter,
  actions = [],
  confirmations = {},
  className = "",
  borderColor = "border-l-primary",
}) => {
  const [openDialogs, setOpenDialogs] = useState(
    Object.keys(confirmations).reduce((acc, key) => {
      acc[key] = false;
      return acc;
    }, {})
  );

  const toggleDialog = (dialogKey, isOpen) => {
    setOpenDialogs((prev) => ({
      ...prev,
      [dialogKey]: isOpen,
    }));
  };

  const handleAction = (action) => {
    if (action.requiresConfirmation) {
      toggleDialog(action.confirmationKey, true);
    } else if (action.onClick) {
      action.onClick(item);
    }
  };

  const filteredActions = actions.filter(
    (action) => !action.condition || action.condition(item)
  );

  return (
    <>
      <Card
        className={`hover:shadow-sm transition-shadow duration-200 border-l-4 ${borderColor} ${className}`}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            {/* Left side - Icon and content */}
            <div className="flex items-start space-x-3 flex-1">
              {/* Icon */}
              {renderIcon && (
                <div className="flex-shrink-0 mt-1">{renderIcon(item)}</div>
              )}

              {/* Content */}
              <div className="flex-1 min-w-0">
                {/* Title and badges */}
                {renderBadges && (
                  <div className="flex items-center justify-between mb-1">
                    {renderBadges(item)}
                  </div>
                )}

                {/* Main content */}
                {renderContent && renderContent(item)}

                {/* Footer */}
                {renderFooter && renderFooter(item)}
              </div>
            </div>

            {/* Right side - Actions */}
            {filteredActions.length > 0 && (
              <div className="flex-shrink-0 ml-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-muted-foreground hover:text-foreground"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {filteredActions.map((action, index) => (
                      <DropdownMenuItem
                        key={index}
                        onClick={() => handleAction(action)}
                        className={action.className || ""}
                        disabled={action.disabled}
                      >
                        {action.icon && (
                          <span className="mr-2">{action.icon}</span>
                        )}
                        {action.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Confirmation Dialogs */}
      {Object.entries(confirmations).map(([key, config]) => (
        <AlertDialog
          key={key}
          open={openDialogs[key]}
          onOpenChange={(isOpen) => toggleDialog(key, isOpen)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{config.title(item)}</AlertDialogTitle>
              <AlertDialogDescription>
                {config.description(item)}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  config.onConfirm(item);
                  toggleDialog(key, false);
                }}
              >
                {config.actionLabel || "Confirm"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      ))}
    </>
  );
};

export const DataCardCollection = ({
  items,
  renderCard,
  emptyState,
  isLoading,
  skeletonCount = 6,
  className = "",
}) => {
  if (isLoading) {
    return (
      <div className={`space-y-3 ${className}`}>
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <DataCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (!items || items.length === 0) {
    return (
      emptyState || (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-muted-foreground">
            {/* Default empty icon */}
          </div>
          <h3 className="mt-4 text-lg font-semibold">No items found</h3>
          <p className="mt-2 text-muted-foreground">
            No data available at this time.
          </p>
        </div>
      )
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {items.map((item, index) => renderCard(item, index))}
    </div>
  );
};
