import { useEffect, useState } from "react";
import { useNotification } from "@/context/notification-context";
import {
  Bell,
  AlertTriangle,
  PlusCircle,
  Calendar,
  Clock,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Send,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { DataCard, DataCardCollection } from "@/components/data-card";

const NotificationDirectory = () => {
  const {
    notifications,
    isLoading,
    fetchAllNotifications,
    removeNotification,
    sendNotificationNow,
  } = useNotification();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");

  useEffect(() => {
    fetchAllNotifications();
  }, []);

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || notification.status === filterStatus;
    const matchesPriority =
      filterPriority === "all" || notification.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Notification Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Notifications" },
          ]}
          actions={[
            {
              label: "New Notification",
              icon: PlusCircle,
              href: "/dashboard/notifications/create",
            },
          ]}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Notifications"
            value={notifications.length}
            description="All notifications"
            icon={Bell}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="High Priority"
            value={
              notifications.filter(
                (notification) => notification.priority === "High"
              ).length
            }
            description="Urgent notifications"
            icon={AlertTriangle}
            isLoading={isLoading}
            trend="negative"
          />

          <StatCard
            title="Scheduled"
            value={
              notifications.filter(
                (notification) => notification.status === "scheduled"
              ).length
            }
            description="Pending delivery"
            icon={Calendar}
            isLoading={isLoading}
          />

          <StatCard
            title="Recent Notifications"
            value={
              notifications.filter((notification) => {
                const createdAt = new Date(notification.createdAt);
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return createdAt >= sevenDaysAgo;
              }).length
            }
            description="Sent in last 7 days"
            icon={Clock}
            isLoading={isLoading}
          />
        </div>

        <div className="grid grid-cols-12 gap-8">
          {/* Filters Sidebar */}
          <div className="col-span-12 lg:col-span-3">
            <Card>
              <CardHeader className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                <h3 className="text-2xl font-bold">Filters</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                    <div className="flex flex-col sm:flex-row gap-4 flex-1">
                      <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                          type="text"
                          placeholder="Search notifications..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>
                  <Select
                    value={filterStatus}
                    onValueChange={setFilterStatus}
                    className="w-full"
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={filterPriority}
                    onValueChange={setFilterPriority}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priority</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="Low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Notifications Area */}
          <div className="col-span-12 lg:col-span-9">
            <div className="space-y-4">
              {isLoading ? (
                <NotificationCardsSkeleton />
              ) : (
                <NotificationCards
                  notifications={filteredNotifications}
                  onEdit={(id) =>
                    navigate(`/dashboard/notifications/${id}/edit`)
                  }
                  onView={(id) => navigate(`/dashboard/notifications/${id}`)}
                  onDelete={removeNotification}
                  onSend={sendNotificationNow}
                />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

// Utility functions for notification display
const notificationUtils = {
  getIcon: (type) => {
    switch (type) {
      case "Emergency":
        return <AlertTriangle className="h-4 w-4 text-destructive" />;
      case "Event":
        return <Calendar className="h-4 w-4 text-success" />;
      case "Academic":
        return <Clock className="h-4 w-4 text-warning" />;
      default:
        return <Bell className="h-4 w-4 text-primary" />;
    }
  },

  getPriorityBadge: (priority) => {
    const colors = {
      High: "bg-destructive/10 text-destructive border-destructive/20",
      Medium: "bg-warning/10 text-warning border-warning/20",
      Low: "bg-secondary text-secondary-foreground border-border",
    };
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
          colors[priority] || colors.Low
        }`}
      >
        {priority}
      </span>
    );
  },

  getStatusIndicator: (status) => {
    const colors = {
      draft: "bg-muted-foreground",
      scheduled: "bg-warning",
      sent: "bg-success",
      failed: "bg-destructive",
    };
    return (
      <div
        className={`w-2 h-2 rounded-full ${colors[status] || colors.draft}`}
      />
    );
  },

  getRecipientText: (recipients, specificClass) => {
    switch (recipients) {
      case "all-users":
        return "All Users";
      case "all-students":
        return "All Students";
      case "all-teachers":
        return "All Teachers";
      case "all-parents":
        return "All Parents";
      case "all-admins":
        return "All Admins";
      case "specific-class":
        return specificClass?.name || "Specific Class";
      case "specific-users":
        return "Specific Users";
      default:
        return recipients;
    }
  },

  formatTimeAgo: (date) => {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInHours = Math.floor((now - notificationDate) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - notificationDate) / (1000 * 60));
      return diffInMinutes <= 1 ? "Just now" : `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
    }
  },
};

// Notification Cards Component
const NotificationCards = ({
  notifications,
  onEdit,
  onView,
  onDelete,
  onSend,
}) => {
  // Empty state component for when no notifications are found
  const emptyState = (
    <div className="text-center py-12">
      <Bell className="mx-auto h-12 w-12 text-muted-foreground" />
      <h3 className="mt-4 text-lg font-semibold">No notifications found</h3>
      <p className="mt-2 text-muted-foreground">
        Get started by creating your first notification.
      </p>
    </div>
  );

  // Render a single notification card
  const renderNotificationCard = (notification) => {
    // Define actions for the notification
    const actions = [
      {
        label: "View",
        icon: <Bell className="h-4 w-4" />,
        onClick: () => onView(notification._id),
      },
      {
        label: "Edit",
        icon: <Edit className="h-4 w-4" />,
        onClick: () => onEdit(notification._id),
        condition: (item) => item.status !== "sent",
      },
      {
        label: "Send Now",
        icon: <Send className="h-4 w-4" />,
        onClick: () => {},
        requiresConfirmation: true,
        confirmationKey: "send",
        condition: (item) => item.status === "scheduled",
      },
      {
        label: "Delete",
        icon: <Trash2 className="h-4 w-4" />,
        onClick: () => {},
        requiresConfirmation: true,
        confirmationKey: "delete",
        condition: (item) => item.status !== "sent",
        className: "text-destructive",
      },
    ];

    // Define confirmation dialogs
    const confirmations = {
      delete: {
        title: (item) => `Delete ${item.title}?`,
        description: () =>
          "Are you sure you want to delete this notification? This action cannot be undone.",
        onConfirm: async (item) => {
          try {
            if (item.status === "sent") {
              toast.error(
                "Cannot delete a notification that has already been sent"
              );
              return;
            }
            await onDelete(item._id);
            toast.success(`${item.title} deleted successfully.`);
          } catch (error) {
            console.error("Failed to delete notification:", error);
            toast.error("Failed to delete notification. Please try again.");
          }
        },
        actionLabel: "Delete",
      },
      send: {
        title: (item) => `Send ${item.title} now?`,
        description: () =>
          "This will send the notification immediately instead of waiting for the scheduled time. Are you sure you want to proceed?",
        onConfirm: async (item) => {
          try {
            if (item.status === "sent") {
              toast.error("Notification has already been sent");
              return;
            }
            await onSend(item._id);
            toast.success(`${item.title} sent successfully.`);
          } catch (error) {
            console.error("Failed to send notification:", error);
            toast.error("Failed to send notification. Please try again.");
          }
        },
        actionLabel: "Send Now",
      },
    };

    return (
      <DataCard
        key={notification._id}
        item={notification}
        renderIcon={(item) => notificationUtils.getIcon(item.type)}
        renderBadges={(item) => (
          <>
            <h3 className="text-sm font-semibold text-foreground line-clamp-1">
              {item.title}
            </h3>
            <div className="flex items-center space-x-2 ml-4">
              {notificationUtils.getPriorityBadge(item.priority)}
              {notificationUtils.getStatusIndicator(item.status)}
            </div>
          </>
        )}
        renderContent={(item) => (
          <>
            <div className="flex items-center text-xs text-muted-foreground mb-2">
              <span className="font-medium">
                {notificationUtils.getRecipientText(
                  item.recipients,
                  item.specificClass
                )}
              </span>
              <span className="mx-1">•</span>
              <span>{item.type}</span>
            </div>

            <p className="text-sm text-foreground/80 line-clamp-2 mb-2">
              {item.message}
            </p>
          </>
        )}
        renderFooter={(item) => (
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">
              {notificationUtils.formatTimeAgo(item.createdAt)}
            </span>
            <button
              onClick={() => onView(item._id)}
              className="text-xs text-primary hover:text-primary/80 font-medium"
            >
              View Details
            </button>
          </div>
        )}
        actions={actions}
        confirmations={confirmations}
        borderColor="border-l-primary"
      />
    );
  };

  return (
    <DataCardCollection
      items={notifications}
      renderCard={renderNotificationCard}
      emptyState={emptyState}
      isLoading={false}
    />
  );
};

// Skeleton Loading Component
const NotificationCardsSkeleton = () => {
  return <DataCardCollection isLoading={true} skeletonCount={6} />;
};

export default NotificationDirectory;
