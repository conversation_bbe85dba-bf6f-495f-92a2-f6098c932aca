import { useEffect, useState } from "react";
import { useNotification } from "@/context/notification-context";
import {
  Bell,
  AlertTriangle,
  PlusCircle,
  Calendar,
  Clock,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Send,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatTimeAgo } from "@/utils/date-filters";
import { cn } from "@/lib/utils";
import { StatusBadge } from "@/components/data-card/data-card-component/status-badge";
import {
  DropdownActions,
  createViewAction,
  createEditAction,
  createDeleteAction,
  createSendAction,
  conditions,
} from "@/components/data-card/data-card-component/dropdown-actions";

const NotificationDirectory = () => {
  const {
    notifications,
    isLoading,
    fetchAllNotifications,
    removeNotification,
    sendNotificationNow,
  } = useNotification();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");

  useEffect(() => {
    fetchAllNotifications();
  }, []);

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || notification.status === filterStatus;
    const matchesPriority =
      filterPriority === "all" || notification.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Notification Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Notifications" },
          ]}
          actions={[
            {
              label: "New Notification",
              icon: PlusCircle,
              href: "/dashboard/notifications/create",
            },
          ]}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Notifications"
            value={notifications.length}
            description="All notifications"
            icon={Bell}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="High Priority"
            value={
              notifications.filter(
                (notification) => notification.priority === "High"
              ).length
            }
            description="Urgent notifications"
            icon={AlertTriangle}
            isLoading={isLoading}
            trend="negative"
          />

          <StatCard
            title="Scheduled"
            value={
              notifications.filter(
                (notification) => notification.status === "scheduled"
              ).length
            }
            description="Pending delivery"
            icon={Calendar}
            isLoading={isLoading}
          />

          <StatCard
            title="Recent Notifications"
            value={
              notifications.filter((notification) => {
                const createdAt = new Date(notification.createdAt);
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return createdAt >= sevenDaysAgo;
              }).length
            }
            description="Sent in last 7 days"
            icon={Clock}
            isLoading={isLoading}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Filters Sidebar */}
          <div className="">
            <Card>
              <CardHeader className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                <h3 className="text-2xl font-bold">Filters</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                    <div className="flex flex-col sm:flex-row gap-4 flex-1 w-full">
                      <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                        <Input
                          type="text"
                          placeholder="Search notifications..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>
                  <Select
                    value={filterStatus}
                    onValueChange={setFilterStatus}
                    className="w-full"
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={filterPriority}
                    onValueChange={setFilterPriority}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priority</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="Low">Low</SelectItem>
                    </SelectContent>
                  </Select>{" "}
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Notifications Area */}
          <div className="col-span-2">
            <div className="space-y-4">
              {isLoading ? (
                <NotificationCardsSkeleton />
              ) : (
                <NotificationCards
                  notifications={filteredNotifications}
                  onEdit={(id) =>
                    navigate(`/dashboard/notifications/${id}/edit`)
                  }
                  onView={(id) => navigate(`/dashboard/notifications/${id}`)}
                  onDelete={removeNotification}
                  onSend={sendNotificationNow}
                />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

// Notification Cards Component
const NotificationCards = ({
  notifications,
  onEdit,
  onView,
  onDelete,
  onSend,
}) => {
  if (notifications.length === 0) {
    return (
      <div className="text-center py-12">
        <Bell className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No notifications found</h3>
        <p className="mt-2 text-muted-foreground">
          Get started by creating your first notification.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification) => (
        <NotificationCard
          key={notification._id}
          notification={notification}
          onEdit={onEdit}
          onView={onView}
          onDelete={onDelete}
          onSend={onSend}
        />
      ))}
    </div>
  );
};

// Individual Notification Card Component
const NotificationCard = ({
  notification,
  onEdit,
  onView,
  onDelete,
  onSend,
}) => {
  const [openDelete, setOpenDelete] = useState(false);
  const [openSend, setOpenSend] = useState(false);

  const getNotificationIcon = (type) => {
    switch (type) {
      case "Emergency":
        return <AlertTriangle className="h-4 w-4 text-destructive" />;
      case "Event":
        return <Calendar className="h-4 w-4 text-success" />;
      case "Academic":
        return <Clock className="h-4 w-4 text-warning" />;
      default:
        return <Bell className="h-4 w-4 text-primary" />;
    }
  };

  const handleDelete = async () => {
    try {
      if (notification.status === "sent") {
        toast.error("Cannot delete a notification that has already been sent");
        setOpenDelete(false);
        return;
      }
      await onDelete(notification._id);
      toast.success(`${notification.title} deleted successfully.`);
      setOpenDelete(false);
    } catch (error) {
      console.error("Failed to delete notification:", error);
      toast.error("Failed to delete notification. Please try again.");
    }
  };

  const handleSend = async () => {
    try {
      if (notification.status === "sent") {
        toast.error("Notification has already been sent");
        setOpenSend(false);
        return;
      }
      await onSend(notification._id);
      toast.success(`${notification.title} sent successfully.`);
      setOpenSend(false);
    } catch (error) {
      console.error("Failed to send notification:", error);
      toast.error("Failed to send notification. Please try again.");
    }
  };

  return (
    <>
      <Card
        className={cn(
          "transition-all duration-200 hover:shadow-md cursor-pointer",
          notification.priority === "High" &&
            "border-l-4 border-l-primary bg-accent/30"
        )}
      >
        <CardContent className="px-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <div className="flex-shrink-0 mt-1">
                {getNotificationIcon(notification.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-sm font-semibold text-foreground line-clamp-1">
                    {notification.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <StatusBadge
                      row={{ original: notification }}
                      statusField="priority"
                      variant={{
                        Low: "success",
                        High: "destructive",
                        Medium: "warning",
                      }}
                    />

                    <StatusBadge
                      row={{ original: notification }}
                      showText={false}
                      statusField="status"
                      variant={{
                        draft: "secondary",
                        scheduled: "warning",
                        sent: "success",
                        failed: "destructive",
                      }}
                    />
                  </div>
                </div>

                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <span className="font-medium capitalize">
                    {notification.recipients}
                  </span>
                  <span className="mx-1">•</span>
                  <span>{notification.type}</span>
                </div>

                <p className="text-sm text-foreground/80 line-clamp-2 mb-2">
                  {notification.message}
                </p>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    {formatTimeAgo(notification.createdAt)}
                  </span>
                  <button
                    onClick={() => onView(notification._id)}
                    className="text-xs text-primary hover:text-primary/80 font-medium"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>

            <DropdownActions
              row={{ original: notification }}
              actions={[
                createViewAction(() => onView(notification._id)),
                createEditAction(() => onEdit(notification._id), {
                  condition: conditions.statusIn(["scheduled", "draft"]),
                }),
                createDeleteAction(() => onDelete(notification._id), {
                  condition: conditions.statusNotEquals("sent"),
                }),
              ]}
            />

            {/* Right side - Actions */}
            {/* <div className="flex-shrink-0 ml-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-muted-foreground hover:text-foreground"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onView(notification._id)}>
                    <Bell className="mr-2 h-4 w-4" />
                    View
                  </DropdownMenuItem>
                  {notification.status !== "sent" && (
                    <DropdownMenuItem onClick={() => onEdit(notification._id)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                  )}
                  {notification.status === "scheduled" && (
                    <DropdownMenuItem onClick={() => setOpenSend(true)}>
                      <Send className="mr-2 h-4 w-4" />
                      Send Now
                    </DropdownMenuItem>
                  )}
                  {notification.status !== "sent" && (
                    <DropdownMenuItem
                      onClick={() => setOpenDelete(true)}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div> */}
          </div>
        </CardContent>
      </Card>

      {/* Delete Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {notification.title}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this notification? This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Send Dialog */}
      <AlertDialog open={openSend} onOpenChange={setOpenSend}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Send {notification.title} now?</AlertDialogTitle>
            <AlertDialogDescription>
              This will send the notification immediately instead of waiting for
              the scheduled time. Are you sure you want to proceed?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSend}>Send Now</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

// Skeleton Loading Component
const NotificationCardsSkeleton = () => {
  return (
    <div className="space-y-3">
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index} className="border-l-4 border-l-muted">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <Skeleton className="h-4 w-4 rounded-full mt-1" />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-48" />
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-5 w-12 rounded-full" />
                      <Skeleton className="h-2 w-2 rounded-full" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-3 w-1" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <div className="flex items-center justify-between pt-1">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
              </div>
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default NotificationDirectory;
